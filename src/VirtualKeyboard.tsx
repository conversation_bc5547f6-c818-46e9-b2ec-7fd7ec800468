import React from 'react';

interface VirtualKeyboardProps {
  onKeyPress: (key: string) => void;
  onBackspace: () => void;
  onClear: () => void;
}

export const VirtualKeyboard: React.FC<VirtualKeyboardProps> = ({
  onKeyPress,
  onBackspace,
  onClear
}) => {
  const rows = [
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
    ['z', 'x', 'c', 'v', 'b', 'n', 'm']
  ];

  const keyStyle: React.CSSProperties = {
    padding: '12px 16px',
    margin: '2px',
    backgroundColor: '#f0f0f0',
    border: '1px solid #ccc',
    borderRadius: '6px',
    cursor: 'pointer',
    fontSize: '16px',
    fontWeight: '500',
    minWidth: '40px',
    textAlign: 'center',
    userSelect: 'none',
    transition: 'all 0.1s ease'
  };

  const specialKeyStyle: React.CSSProperties = {
    ...keyStyle,
    backgroundColor: '#e0e0e0',
    minWidth: '80px'
  };

  const spaceKeyStyle: React.CSSProperties = {
    ...keyStyle,
    minWidth: '200px'
  };

  const handleKeyClick = (key: string) => {
    onKeyPress(key);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent input from losing focus
  };

  return (
    <div style={{
      padding: '20px',
      backgroundColor: '#f8f9fa',
      borderRadius: '12px',
      border: '1px solid #dee2e6',
      maxWidth: '600px',
      margin: '20px auto'
    }}>
      <h3 style={{ 
        margin: '0 0 15px 0', 
        textAlign: 'center', 
        color: '#495057',
        fontSize: '18px'
      }}>
        Virtual Keyboard
      </h3>
      
      {/* Letter rows */}
      {rows.map((row, rowIndex) => (
        <div key={rowIndex} style={{
          display: 'flex',
          justifyContent: 'center',
          marginBottom: '4px'
        }}>
          {row.map((key) => (
            <button
              key={key}
              style={keyStyle}
              onMouseDown={handleMouseDown}
              onClick={() => handleKeyClick(key)}
              onMouseEnter={(e) => {
                (e.target as HTMLElement).style.backgroundColor = '#e9ecef';
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLElement).style.backgroundColor = '#f0f0f0';
              }}
            >
              {key.toUpperCase()}
            </button>
          ))}
        </div>
      ))}
      
      {/* Space and special keys row */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        marginTop: '8px',
        gap: '4px'
      }}>
        <button
          style={specialKeyStyle}
          onMouseDown={handleMouseDown}
          onClick={onBackspace}
          onMouseEnter={(e) => {
            (e.target as HTMLElement).style.backgroundColor = '#d6d8db';
          }}
          onMouseLeave={(e) => {
            (e.target as HTMLElement).style.backgroundColor = '#e0e0e0';
          }}
        >
          ⌫ Backspace
        </button>
        
        <button
          style={spaceKeyStyle}
          onMouseDown={handleMouseDown}
          onClick={() => handleKeyClick(' ')}
          onMouseEnter={(e) => {
            (e.target as HTMLElement).style.backgroundColor = '#e9ecef';
          }}
          onMouseLeave={(e) => {
            (e.target as HTMLElement).style.backgroundColor = '#f0f0f0';
          }}
        >
          Space
        </button>
        
        <button
          style={specialKeyStyle}
          onMouseDown={handleMouseDown}
          onClick={onClear}
          onMouseEnter={(e) => {
            (e.target as HTMLElement).style.backgroundColor = '#d6d8db';
          }}
          onMouseLeave={(e) => {
            (e.target as HTMLElement).style.backgroundColor = '#e0e0e0';
          }}
        >
          Clear
        </button>
      </div>
      
      {/* Test suggestions */}
      <div style={{
        marginTop: '15px',
        padding: '12px',
        backgroundColor: '#d1ecf1',
        borderRadius: '6px',
        border: '1px solid #bee5eb'
      }}>
        <div style={{ fontSize: '14px', color: '#0c5460', marginBottom: '8px' }}>
          <strong>🧪 Test Cases:</strong>
        </div>
        <div style={{ fontSize: '13px', color: '#0c5460', lineHeight: '1.4' }}>
          <div><strong>Main Fix:</strong> "poo" → "***" → continue typing "l" → should become "pool"</div>
          <div style={{ marginTop: '4px' }}><strong>Other tests:</strong> "damn" → "damned" | "hell" → "hello" | "ass" → "class"</div>
          <div style={{ marginTop: '4px' }}><strong>Normal words:</strong> "hello", "world", "test", "keyboard"</div>
        </div>
      </div>

      {/* Instructions */}
      <div style={{
        marginTop: '10px',
        padding: '10px',
        backgroundColor: '#f8d7da',
        borderRadius: '6px',
        border: '1px solid #f5c6cb'
      }}>
        <div style={{ fontSize: '12px', color: '#721c24' }}>
          <strong>💡 Tip:</strong> You can use both the virtual keyboard and your physical keyboard.
          The virtual keyboard helps simulate touch input scenarios.
        </div>
      </div>
    </div>
  );
};
