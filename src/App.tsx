import { useState, useRef } from 'react';
import { MaskedInput, type MaskedInputRef } from './MaskedInput';
import { VirtualKeyboard } from './VirtualKeyboard';
import './App.css';

function App() {
  const [inputValue, setInputValue] = useState('');
  const maskedInputRef = useRef<MaskedInputRef>(null);

  const handleVirtualKeyPress = (key: string) => {
    maskedInputRef.current?.handleVirtualKeyPress(key);
  };

  const handleVirtualBackspace = () => {
    maskedInputRef.current?.handleVirtualBackspace();
  };

  const handleVirtualClear = () => {
    maskedInputRef.current?.handleVirtualClear();
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1 style={{ textAlign: 'center', color: '#333', marginBottom: '30px' }}>
        Masked Input Demo with Virtual Keyboard
      </h1>

      <div style={{ marginBottom: '30px' }}>
        <label style={{
          display: 'block',
          marginBottom: '12px',
          fontSize: '16px',
          fontWeight: '500',
          color: '#495057'
        }}>
          Type something (try "poo" → "pool", "damn" → "damned", etc.):
        </label>
        <MaskedInput
          ref={maskedInputRef}
          value={inputValue}
          onChange={setInputValue}
          placeholder="Start typing or use the virtual keyboard below..."
          className="masked-input"
        />
      </div>

      <VirtualKeyboard
        onKeyPress={handleVirtualKeyPress}
        onBackspace={handleVirtualBackspace}
        onClear={handleVirtualClear}
      />

      <div style={{
        marginTop: '30px',
        padding: '15px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        border: '1px solid #dee2e6'
      }}>
        <div style={{ marginBottom: '10px' }}>
          <strong style={{ color: '#495057' }}>Actual value:</strong>
          <span style={{
            marginLeft: '10px',
            padding: '4px 8px',
            backgroundColor: '#fff',
            border: '1px solid #ced4da',
            borderRadius: '4px',
            fontFamily: 'monospace'
          }}>
            "{inputValue}"
          </span>
        </div>
        <div style={{ fontSize: '14px', color: '#6c757d' }}>
          Length: {inputValue.length} characters
        </div>
      </div>
    </div>
  );
}

export default App;
