import React, { useState } from 'react';
import { MaskedInput } from './MaskedInput';

function App() {
  const [inputValue, setInputValue] = useState('');

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h1>Masked Input Demo</h1>
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', marginBottom: '8px' }}>
          Type something (try "poo", "pool", or other words):
        </label>
        <MaskedInput
          value={inputValue}
          onChange={setInputValue}
          placeholder="Start typing..."
          className="masked-input"
        />
      </div>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
        <strong>Actual value:</strong> {inputValue}
      </div>
    </div>
  );
}

export default App;
