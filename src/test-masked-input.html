<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Masked Input</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            width: 100%;
            padding: 8px 12px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin: 10px 0;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Masked Input Test Cases</h1>
    
    <div class="test-case">
        <h3>Test Case 1: "poo" → "pool"</h3>
        <p>Instructions: Type "poo" (should become "***"), then type "l" (should become "pool")</p>
        <input type="text" class="test-input" id="test1" placeholder="Type 'poo' then 'l'">
        <div class="result">
            <strong>Expected:</strong> "poo" → "***" → "pool"<br>
            <strong>Actual:</strong> <span id="result1">-</span>
        </div>
    </div>

    <div class="test-case">
        <h3>Test Case 2: Other bad words</h3>
        <p>Instructions: Try typing other words from the bad words list</p>
        <input type="text" class="test-input" id="test2" placeholder="Try 'damn', 'hell', etc.">
        <div class="result">
            <strong>Result:</strong> <span id="result2">-</span>
        </div>
    </div>

    <div class="test-case">
        <h3>Test Case 3: Normal words</h3>
        <p>Instructions: Type normal words that should not be masked</p>
        <input type="text" class="test-input" id="test3" placeholder="Try 'hello', 'world', 'pool'">
        <div class="result">
            <strong>Result:</strong> <span id="result3">-</span>
        </div>
    </div>

    <script>
        // Simple test to show input values
        document.getElementById('test1').addEventListener('input', function(e) {
            document.getElementById('result1').textContent = e.target.value;
        });
        
        document.getElementById('test2').addEventListener('input', function(e) {
            document.getElementById('result2').textContent = e.target.value;
        });
        
        document.getElementById('test3').addEventListener('input', function(e) {
            document.getElementById('result3').textContent = e.target.value;
        });
    </script>
</body>
</html>
