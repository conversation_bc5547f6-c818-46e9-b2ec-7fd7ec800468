import React, { useState, useRef, useEffect } from 'react';
import { bad_words } from './bad';

interface MaskedInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export interface MaskedInputRef {
  handleVirtualKeyPress: (key: string) => void;
  handleVirtualBackspace: () => void;
  handleVirtualClear: () => void;
  focus: () => void;
}

export const MaskedInput = React.forwardRef<MaskedInputRef, MaskedInputProps>(({
  value = '',
  onChange,
  placeholder,
  className
}, ref) => {
  const [displayValue, setDisplayValue] = useState('');
  const [actualValue, setActualValue] = useState(value);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [prevDisplayValue, setPrevDisplayValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  const maskBadWords = (text: string): string => {
    let maskedText = text;

    // Sort bad words by length (longest first) to handle overlapping matches
    const sortedBadWords = [...bad_words].sort((a, b) => b.length - a.length);

    for (const badWord of sortedBadWords) {
      const regex = new RegExp(`\\b${badWord.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
      maskedText = maskedText.replace(regex, (match) => '*'.repeat(match.length));
    }

    return maskedText;
  };

  const updateDisplay = (newActualValue: string) => {
    const masked = maskBadWords(newActualValue);
    setDisplayValue(masked);
    setPrevDisplayValue(masked);
    setActualValue(newActualValue);
    onChange?.(newActualValue);
  };

  const reconstructActualValue = (newDisplayValue: string, prevDisplay: string, prevActual: string): string => {
    // If the display value hasn't changed, return the previous actual value
    if (newDisplayValue === prevDisplay) {
      return prevActual;
    }

    // Find the difference between the new and previous display values
    let insertionPoint = 0;
    let deletionLength = 0;
    let insertedText = '';

    // Find where the change occurred
    while (insertionPoint < Math.min(newDisplayValue.length, prevDisplay.length) &&
           newDisplayValue[insertionPoint] === prevDisplay[insertionPoint]) {
      insertionPoint++;
    }

    // Find how much was deleted from the end
    let endMatchLength = 0;
    while (endMatchLength < Math.min(newDisplayValue.length - insertionPoint, prevDisplay.length - insertionPoint) &&
           newDisplayValue[newDisplayValue.length - 1 - endMatchLength] ===
           prevDisplay[prevDisplay.length - 1 - endMatchLength]) {
      endMatchLength++;
    }

    deletionLength = prevDisplay.length - insertionPoint - endMatchLength;
    insertedText = newDisplayValue.slice(insertionPoint, newDisplayValue.length - endMatchLength);

    // Apply the same changes to the actual value
    const beforeChange = prevActual.slice(0, insertionPoint);
    const afterChange = prevActual.slice(insertionPoint + deletionLength);

    return beforeChange + insertedText + afterChange;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDisplayValue = e.target.value;
    const newCursorPos = e.target.selectionStart || 0;

    // Reconstruct what the user actually intended to type
    const newActualValue = reconstructActualValue(newDisplayValue, prevDisplayValue, actualValue);

    setCursorPosition(newCursorPos);
    updateDisplay(newActualValue);
  };

  const handleVirtualKeyPress = (key: string) => {
    const currentPos = cursorPosition;
    const beforeCursor = actualValue.slice(0, currentPos);
    const afterCursor = actualValue.slice(currentPos);
    const newActualValue = beforeCursor + key + afterCursor;

    setCursorPosition(currentPos + key.length);
    updateDisplay(newActualValue);

    // Focus the input to maintain cursor position
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleVirtualBackspace = () => {
    if (cursorPosition > 0) {
      const beforeCursor = actualValue.slice(0, cursorPosition - 1);
      const afterCursor = actualValue.slice(cursorPosition);
      const newActualValue = beforeCursor + afterCursor;

      setCursorPosition(cursorPosition - 1);
      updateDisplay(newActualValue);
    }

    // Focus the input to maintain cursor position
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleVirtualClear = () => {
    setCursorPosition(0);
    updateDisplay('');

    // Focus the input
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Expose methods through ref
  React.useImperativeHandle(ref, () => ({
    handleVirtualKeyPress,
    handleVirtualBackspace,
    handleVirtualClear,
    focus: () => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  }));

  const handleClick = (e: React.MouseEvent<HTMLInputElement>) => {
    const input = e.target as HTMLInputElement;
    const clickPosition = input.selectionStart || 0;
    setCursorPosition(clickPosition);
  };

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.setSelectionRange(cursorPosition, cursorPosition);
    }
  }, [displayValue, cursorPosition]);

  useEffect(() => {
    const masked = maskBadWords(value);
    setDisplayValue(masked);
    setPrevDisplayValue(masked);
    setActualValue(value);
  }, [value]);

  return (
    <input
      ref={inputRef}
      type="text"
      value={displayValue}
      onChange={handleInputChange}
      onClick={handleClick}
      placeholder={placeholder}
      className={className}
      style={{
        fontSize: '16px',
        padding: '8px 12px',
        border: '1px solid #ccc',
        borderRadius: '4px',
        outline: 'none',
        WebkitUserSelect: 'none' as const,
        userSelect: 'none' as const
      }}
    />
  );
});

MaskedInput.displayName = 'MaskedInput';