import React, { useState, useRef, useEffect } from 'react';
import { bad_words } from './bad';

interface MaskedInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export const MaskedInput: React.FC<MaskedInputProps> = ({
  value = '',
  onChange,
  placeholder,
  className
}) => {
  const [displayValue, setDisplayValue] = useState('');
  const [actualValue, setActualValue] = useState(value);
  const [cursorPosition, setCursorPosition] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);

  const maskBadWords = (text: string): string => {
    let maskedText = text;
    
    // Sort bad words by length (longest first) to handle overlapping matches
    const sortedBadWords = [...bad_words].sort((a, b) => b.length - a.length);
    
    for (const badWord of sortedBadWords) {
      const regex = new RegExp(`\\b${badWord.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
      maskedText = maskedText.replace(regex, (match) => '*'.repeat(match.length));
    }
    
    return maskedText;
  };

  const updateDisplay = (newValue: string) => {
    const masked = maskBadWords(newValue);
    setDisplayValue(masked);
    setActualValue(newValue);
    onChange?.(newValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    const newCursorPos = e.target.selectionStart || 0;
    
    setCursorPosition(newCursorPos);
    updateDisplay(newValue);
  };

  const handleClick = (e: React.MouseEvent<HTMLInputElement>) => {
    const input = e.target as HTMLInputElement;
    const clickPosition = input.selectionStart || 0;
    setCursorPosition(clickPosition);
  };

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.setSelectionRange(cursorPosition, cursorPosition);
    }
  }, [displayValue, cursorPosition]);

  useEffect(() => {
    updateDisplay(value);
  }, [value]);

  return (
    <input
      ref={inputRef}
      type="text"
      value={displayValue}
      onChange={handleInputChange}
      onClick={handleClick}
      placeholder={placeholder}
      className={className}
      style={{
        fontSize: '16px',
        padding: '8px 12px',
        border: '1px solid #ccc',
        borderRadius: '4px',
        outline: 'none',
        ...({ WebkitUserSelect: 'none', userSelect: 'none' } as any)
      }}
    />
  );
};