import React, { useState, useRef, useEffect } from 'react';
import { bad_words } from './bad';

interface MaskedInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export const MaskedInput: React.FC<MaskedInputProps> = ({
  value = '',
  onChange,
  placeholder,
  className
}) => {
  const [displayValue, setDisplayValue] = useState('');
  const [actualValue, setActualValue] = useState(value);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [prevDisplayValue, setPrevDisplayValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  const maskBadWords = (text: string): string => {
    let maskedText = text;

    // Sort bad words by length (longest first) to handle overlapping matches
    const sortedBadWords = [...bad_words].sort((a, b) => b.length - a.length);

    for (const badWord of sortedBadWords) {
      const regex = new RegExp(`\\b${badWord.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
      maskedText = maskedText.replace(regex, (match) => '*'.repeat(match.length));
    }

    return maskedText;
  };

  const updateDisplay = (newActualValue: string) => {
    const masked = maskBadWords(newActualValue);
    setDisplayValue(masked);
    setPrevDisplayValue(masked);
    setActualValue(newActualValue);
    onChange?.(newActualValue);
  };

  const reconstructActualValue = (newDisplayValue: string, prevDisplay: string, prevActual: string): string => {
    // If the display value hasn't changed, return the previous actual value
    if (newDisplayValue === prevDisplay) {
      return prevActual;
    }

    // Find the difference between the new and previous display values
    let insertionPoint = 0;
    let deletionLength = 0;
    let insertedText = '';

    // Find where the change occurred
    while (insertionPoint < Math.min(newDisplayValue.length, prevDisplay.length) &&
           newDisplayValue[insertionPoint] === prevDisplay[insertionPoint]) {
      insertionPoint++;
    }

    // Find how much was deleted from the end
    let endMatchLength = 0;
    while (endMatchLength < Math.min(newDisplayValue.length - insertionPoint, prevDisplay.length - insertionPoint) &&
           newDisplayValue[newDisplayValue.length - 1 - endMatchLength] ===
           prevDisplay[prevDisplay.length - 1 - endMatchLength]) {
      endMatchLength++;
    }

    deletionLength = prevDisplay.length - insertionPoint - endMatchLength;
    insertedText = newDisplayValue.slice(insertionPoint, newDisplayValue.length - endMatchLength);

    // Apply the same changes to the actual value
    const beforeChange = prevActual.slice(0, insertionPoint);
    const afterChange = prevActual.slice(insertionPoint + deletionLength);

    return beforeChange + insertedText + afterChange;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDisplayValue = e.target.value;
    const newCursorPos = e.target.selectionStart || 0;

    // Reconstruct what the user actually intended to type
    const newActualValue = reconstructActualValue(newDisplayValue, prevDisplayValue, actualValue);

    setCursorPosition(newCursorPos);
    updateDisplay(newActualValue);
  };

  const handleClick = (e: React.MouseEvent<HTMLInputElement>) => {
    const input = e.target as HTMLInputElement;
    const clickPosition = input.selectionStart || 0;
    setCursorPosition(clickPosition);
  };

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.setSelectionRange(cursorPosition, cursorPosition);
    }
  }, [displayValue, cursorPosition]);

  useEffect(() => {
    updateDisplay(value);
  }, [value]);

  return (
    <input
      ref={inputRef}
      type="text"
      value={displayValue}
      onChange={handleInputChange}
      onClick={handleClick}
      placeholder={placeholder}
      className={className}
      style={{
        fontSize: '16px',
        padding: '8px 12px',
        border: '1px solid #ccc',
        borderRadius: '4px',
        outline: 'none',
        ...({ WebkitUserSelect: 'none', userSelect: 'none' } as any)
      }}
    />
  );
};