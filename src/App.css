/* Custom styles for the Masked Input Demo */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
}

.masked-input {
  width: 100%;
  padding: 12px 16px;
  font-size: 18px;
  border: 2px solid #ced4da;
  border-radius: 8px;
  outline: none;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: #fff;
}

.masked-input:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.masked-input::placeholder {
  color: #6c757d;
  opacity: 1;
}

/* Virtual keyboard button hover effects */
.virtual-key {
  transition: all 0.1s ease;
}

.virtual-key:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.virtual-key:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .masked-input {
    font-size: 16px;
    padding: 10px 12px;
  }
}
